<?php
// Security Functions
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Theme Functions
function getCurrentTheme() {
    global $db;
    
    $db->query("SELECT theme_name FROM site_settings WHERE id = 1");
    $result = $db->single();
    
    return $result ? $result['theme_name'] : DEFAULT_THEME;
}

function getSiteSettings() {
    global $db;
    
    $db->query("SELECT * FROM site_settings WHERE id = 1");
    $result = $db->single();
    
    if (!$result) {
        // Return default settings if none exist
        return [
            'site_name' => 'E-Commerce Store',
            'site_description' => 'Your one-stop shop for everything',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567890',
            'address' => '123 Main St, City, Country',
            'logo' => 'assets/images/logo.png',
            'theme_name' => DEFAULT_THEME,
            'currency' => 'USD',
            'tax_rate' => 0.10
        ];
    }
    
    return $result;
}

function getFeatureStatus($feature) {
    global $db;
    
    $db->query("SELECT status FROM feature_toggles WHERE feature_name = :feature");
    $db->bind(':feature', $feature);
    $result = $db->single();
    
    return $result ? (bool)$result['status'] : (FEATURES[$feature] ?? false);
}

// User Functions
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $db;
    $db->query("SELECT * FROM users WHERE id = :id");
    $db->bind(':id', $_SESSION['user_id']);
    return $db->single();
}

function isAdmin() {
    $user = getCurrentUser();
    return $user && $user['role'] === 'admin';
}

// Cart Functions
function getCartItems() {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    return $_SESSION['cart'];
}

function addToCart($productId, $quantity = 1) {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    if (isset($_SESSION['cart'][$productId])) {
        $_SESSION['cart'][$productId] += $quantity;
    } else {
        $_SESSION['cart'][$productId] = $quantity;
    }
}

function removeFromCart($productId) {
    if (isset($_SESSION['cart'][$productId])) {
        unset($_SESSION['cart'][$productId]);
    }
}

function getCartTotal() {
    global $db;
    $cart = getCartItems();
    $total = 0;
    
    foreach ($cart as $productId => $quantity) {
        $db->query("SELECT price FROM products WHERE id = :id");
        $db->bind(':id', $productId);
        $product = $db->single();
        
        if ($product) {
            $total += $product['price'] * $quantity;
        }
    }
    
    return $total;
}

// Utility Functions
function redirect($url) {
    header("Location: " . $url);
    exit();
}

function formatPrice($price, $currency = 'USD') {
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'JPY' => '¥'
    ];
    
    $symbol = $symbols[$currency] ?? '$';
    return $symbol . number_format($price, 2);
}

function generateSlug($string) {
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string)));
    return $slug;
}

function uploadImage($file, $directory = 'products') {
    $uploadDir = UPLOAD_PATH . $directory . '/';
    
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File too large'];
    }
    
    $fileName = uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return ['success' => true, 'filename' => $fileName, 'path' => $filePath];
    }
    
    return ['success' => false, 'message' => 'Upload failed'];
}

// Email Functions
function sendEmail($to, $subject, $body, $isHTML = true) {
    // This is a basic implementation. In production, use PHPMailer or similar
    $headers = "From: " . ADMIN_EMAIL . "\r\n";
    $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
    
    if ($isHTML) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $body, $headers);
}
?>
