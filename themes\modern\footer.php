    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4"><?= htmlspecialchars($siteSettings['site_name']) ?></h3>
                    <p class="text-gray-300 mb-4"><?= htmlspecialchars($siteSettings['site_description']) ?></p>
                    <div class="space-y-2 text-sm text-gray-300">
                        <?php if (!empty($siteSettings['address'])): ?>
                            <p><i class="fas fa-map-marker-alt mr-2"></i> <?= htmlspecialchars($siteSettings['address']) ?></p>
                        <?php endif; ?>
                        <p><i class="fas fa-phone mr-2"></i> <?= htmlspecialchars($siteSettings['contact_phone']) ?></p>
                        <p><i class="fas fa-envelope mr-2"></i> <?= htmlspecialchars($siteSettings['contact_email']) ?></p>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="<?= SITE_URL ?>" class="hover:text-white transition-colors">Home</a></li>
                        <li><a href="?page=products" class="hover:text-white transition-colors">Products</a></li>
                        <li><a href="?page=about" class="hover:text-white transition-colors">About Us</a></li>
                        <li><a href="?page=contact" class="hover:text-white transition-colors">Contact</a></li>
                        <li><a href="?page=privacy" class="hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="?page=terms" class="hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Customer Service</h3>
                    <ul class="space-y-2 text-gray-300">
                        <?php if (isLoggedIn()): ?>
                            <li><a href="?page=profile" class="hover:text-white transition-colors">My Account</a></li>
                            <li><a href="?page=orders" class="hover:text-white transition-colors">Order History</a></li>
                        <?php endif; ?>
                        <?php if (getFeatureStatus('wishlist')): ?>
                            <li><a href="?page=wishlist" class="hover:text-white transition-colors">Wishlist</a></li>
                        <?php endif; ?>
                        <li><a href="?page=cart" class="hover:text-white transition-colors">Shopping Cart</a></li>
                        <li><a href="?page=shipping" class="hover:text-white transition-colors">Shipping Info</a></li>
                        <li><a href="?page=returns" class="hover:text-white transition-colors">Returns</a></li>
                        <li><a href="?page=faq" class="hover:text-white transition-colors">FAQ</a></li>
                    </ul>
                </div>

                <!-- Newsletter & Social -->
                <div>
                    <?php if (getFeatureStatus('newsletter')): ?>
                        <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
                        <p class="text-gray-300 mb-4">Subscribe to get updates on new products and offers.</p>
                        <form class="mb-6" onsubmit="subscribeNewsletter(event)">
                            <div class="flex">
                                <input type="email" placeholder="Your email" required
                                       class="flex-1 px-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-l-md focus:outline-none focus:border-blue-500">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r-md transition-colors">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Social Media -->
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <?php if (!empty($siteSettings['social_facebook'])): ?>
                            <a href="<?= htmlspecialchars($siteSettings['social_facebook']) ?>" target="_blank" 
                               class="text-gray-300 hover:text-white text-xl transition-colors">
                                <i class="fab fa-facebook"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($siteSettings['social_twitter'])): ?>
                            <a href="<?= htmlspecialchars($siteSettings['social_twitter']) ?>" target="_blank" 
                               class="text-gray-300 hover:text-white text-xl transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($siteSettings['social_instagram'])): ?>
                            <a href="<?= htmlspecialchars($siteSettings['social_instagram']) ?>" target="_blank" 
                               class="text-gray-300 hover:text-white text-xl transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($siteSettings['social_linkedin'])): ?>
                            <a href="<?= htmlspecialchars($siteSettings['social_linkedin']) ?>" target="_blank" 
                               class="text-gray-300 hover:text-white text-xl transition-colors">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-4 md:mb-0">
                        <h4 class="text-sm font-semibold mb-2">We Accept</h4>
                        <div class="flex space-x-4 text-2xl text-gray-300">
                            <i class="fab fa-cc-visa"></i>
                            <i class="fab fa-cc-mastercard"></i>
                            <i class="fab fa-cc-amex"></i>
                            <i class="fab fa-cc-paypal"></i>
                            <i class="fab fa-cc-stripe"></i>
                        </div>
                    </div>
                    
                    <div class="text-center md:text-right">
                        <div class="flex items-center space-x-4 text-sm text-gray-300">
                            <span><i class="fas fa-shield-alt mr-1"></i> Secure Shopping</span>
                            <span><i class="fas fa-truck mr-1"></i> Free Shipping</span>
                            <span><i class="fas fa-undo mr-1"></i> Easy Returns</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; <?= date('Y') ?> <?= htmlspecialchars($siteSettings['site_name']) ?>. All rights reserved.</p>
                <?php if (!empty($siteSettings['footer_text'])): ?>
                    <p class="mt-2 text-sm"><?= htmlspecialchars($siteSettings['footer_text']) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span>Loading...</span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="<?= SITE_URL ?>/themes/modern/assets/js/main.js"></script>
    
    <script>
        // Newsletter subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            fetch('api/newsletter.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Thank you for subscribing!');
                    event.target.reset();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred. Please try again.');
            });
        }

        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.remove('opacity-0', 'invisible');
            } else {
                backToTop.classList.add('opacity-0', 'invisible');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Loading overlay functions
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        // Add to cart function
        function addToCart(productId, quantity = 1) {
            showLoading();
            
            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    product_id: productId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    updateCartCount();
                    // Show success message or animation
                    showNotification('Product added to cart!', 'success');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showNotification('An error occurred. Please try again.', 'error');
            });
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            } text-white`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize tooltips and other interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add any initialization code here
        });
    </script>

    <?php if (getFeatureStatus('live_chat')): ?>
    <!-- Live Chat Widget -->
    <div id="liveChatWidget" class="fixed bottom-4 left-4 z-40">
        <button onclick="toggleLiveChat()" class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg">
            <i class="fas fa-comments"></i>
        </button>
    </div>
    <?php endif; ?>

</body>
</html>
