<?php
// Get search and filter parameters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$sort = $_GET['sort'] ?? 'name';
$order = $_GET['order'] ?? 'ASC';
$page = max(1, intval($_GET['p'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = ["p.status = 'active'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search OR p.short_description LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if (!empty($category)) {
    $whereConditions[] = "c.slug = :category";
    $params[':category'] = $category;
}

$whereClause = implode(' AND ', $whereConditions);

// Valid sort options
$validSorts = ['name', 'price', 'created_at'];
$validOrders = ['ASC', 'DESC'];

if (!in_array($sort, $validSorts)) $sort = 'name';
if (!in_array($order, $validOrders)) $order = 'ASC';

// Get total count
$countQuery = "SELECT COUNT(DISTINCT p.id) as total FROM products p 
               LEFT JOIN product_categories pc ON p.id = pc.product_id 
               LEFT JOIN categories c ON pc.category_id = c.id 
               WHERE $whereClause";

$db->query($countQuery);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$totalResult = $db->single();
$totalProducts = $totalResult['total'];
$totalPages = ceil($totalProducts / $limit);

// Get products
$query = "SELECT DISTINCT p.*, pi.image_url FROM products p 
          LEFT JOIN product_categories pc ON p.id = pc.product_id 
          LEFT JOIN categories c ON pc.category_id = c.id 
          LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
          WHERE $whereClause 
          ORDER BY p.$sort $order 
          LIMIT $limit OFFSET $offset";

$db->query($query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$products = $db->resultset();

// Get categories for filter
$db->query("SELECT * FROM categories WHERE status = 'active' AND parent_id IS NULL ORDER BY name");
$categories = $db->resultset();

$pageTitle = 'Products';
if (!empty($search)) {
    $pageTitle = 'Search Results for "' . htmlspecialchars($search) . '"';
} elseif (!empty($category)) {
    $db->query("SELECT name FROM categories WHERE slug = :slug");
    $db->bind(':slug', $category);
    $categoryData = $db->single();
    if ($categoryData) {
        $pageTitle = htmlspecialchars($categoryData['name']);
    }
}
?>

<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?= SITE_URL ?>" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500"><?= htmlspecialchars($pageTitle) ?></span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2"><?= htmlspecialchars($pageTitle) ?></h1>
            <p class="text-gray-600">
                <?php if ($totalProducts > 0): ?>
                    Showing <?= $offset + 1 ?>-<?= min($offset + $limit, $totalProducts) ?> of <?= $totalProducts ?> products
                <?php else: ?>
                    No products found
                <?php endif; ?>
            </p>
        </div>
        
        <!-- Sort Options -->
        <div class="flex items-center space-x-4 mt-4 md:mt-0">
            <label class="text-sm font-medium text-gray-700">Sort by:</label>
            <select onchange="updateSort(this.value)" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                <option value="name-ASC" <?= $sort === 'name' && $order === 'ASC' ? 'selected' : '' ?>>Name (A-Z)</option>
                <option value="name-DESC" <?= $sort === 'name' && $order === 'DESC' ? 'selected' : '' ?>>Name (Z-A)</option>
                <option value="price-ASC" <?= $sort === 'price' && $order === 'ASC' ? 'selected' : '' ?>>Price (Low to High)</option>
                <option value="price-DESC" <?= $sort === 'price' && $order === 'DESC' ? 'selected' : '' ?>>Price (High to Low)</option>
                <option value="created_at-DESC" <?= $sort === 'created_at' && $order === 'DESC' ? 'selected' : '' ?>>Newest First</option>
                <option value="created_at-ASC" <?= $sort === 'created_at' && $order === 'ASC' ? 'selected' : '' ?>>Oldest First</option>
            </select>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                <h3 class="text-lg font-semibold mb-4">Filters</h3>
                
                <!-- Categories Filter -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-800 mb-3">Categories</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="?page=products<?= !empty($search) ? '&search=' . urlencode($search) : '' ?>" 
                               class="text-sm <?= empty($category) ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600' ?>">
                                All Categories
                            </a>
                        </li>
                        <?php foreach ($categories as $cat): ?>
                            <li>
                                <a href="?page=products&category=<?= $cat['slug'] ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>" 
                                   class="text-sm <?= $category === $cat['slug'] ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600' ?>">
                                    <?= htmlspecialchars($cat['name']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Price Range Filter -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-800 mb-3">Price Range</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" onchange="filterByPrice(0, 25)">
                            <span class="text-sm text-gray-600">Under $25</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" onchange="filterByPrice(25, 50)">
                            <span class="text-sm text-gray-600">$25 - $50</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" onchange="filterByPrice(50, 100)">
                            <span class="text-sm text-gray-600">$50 - $100</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" onchange="filterByPrice(100, 999999)">
                            <span class="text-sm text-gray-600">Over $100</span>
                        </label>
                    </div>
                </div>
                
                <!-- Clear Filters -->
                <?php if (!empty($search) || !empty($category)): ?>
                    <a href="?page=products" class="block w-full text-center bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors">
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="lg:col-span-3">
            <?php if (empty($products)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No products found</h3>
                    <p class="text-gray-500 mb-4">Try adjusting your search or filter criteria</p>
                    <a href="?page=products" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded transition-colors">
                        View All Products
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($products as $product): ?>
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden group product-card">
                            <div class="relative">
                                <a href="?page=product&id=<?= $product['id'] ?>">
                                    <?php if ($product['image_url']): ?>
                                        <img src="<?= SITE_URL ?>/<?= $product['image_url'] ?>" 
                                             alt="<?= htmlspecialchars($product['name']) ?>" 
                                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400 text-3xl"></i>
                                        </div>
                                    <?php endif; ?>
                                </a>
                                
                                <?php if ($product['sale_price']): ?>
                                    <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs rounded">
                                        Sale
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (getFeatureStatus('wishlist')): ?>
                                    <button onclick="addToWishlist(<?= $product['id'] ?>)" 
                                            class="absolute top-2 right-2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                                        <i class="fas fa-heart text-gray-400 hover:text-red-500"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-2 truncate">
                                    <a href="?page=product&id=<?= $product['id'] ?>" class="hover:text-blue-600">
                                        <?= htmlspecialchars($product['name']) ?>
                                    </a>
                                </h3>
                                
                                <?php if ($product['short_description']): ?>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                        <?= htmlspecialchars($product['short_description']) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <?php if ($product['sale_price']): ?>
                                            <span class="text-lg font-bold text-red-600">
                                                <?= formatPrice($product['sale_price'], $siteSettings['currency']) ?>
                                            </span>
                                            <span class="text-sm text-gray-500 line-through">
                                                <?= formatPrice($product['price'], $siteSettings['currency']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-lg font-bold text-gray-800">
                                                <?= formatPrice($product['price'], $siteSettings['currency']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($product['stock_status'] === 'in_stock'): ?>
                                        <button onclick="addToCart(<?= $product['id'] ?>)" 
                                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                            <i class="fas fa-cart-plus mr-1"></i> Add
                                        </button>
                                    <?php else: ?>
                                        <span class="text-red-500 text-sm font-medium">Out of Stock</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="flex justify-center mt-12">
                        <nav class="flex items-center space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=products&p=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>&sort=<?= $sort ?>&order=<?= $order ?>" 
                                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?page=products&p=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>&sort=<?= $sort ?>&order=<?= $order ?>" 
                                   class="px-3 py-2 <?= $i === $page ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' ?> rounded transition-colors">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=products&p=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($category) ? '&category=' . urlencode($category) : '' ?>&sort=<?= $sort ?>&order=<?= $order ?>" 
                                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    function updateSort(value) {
        const [sort, order] = value.split('-');
        const url = new URL(window.location);
        url.searchParams.set('sort', sort);
        url.searchParams.set('order', order);
        window.location.href = url.toString();
    }
    
    function filterByPrice(min, max) {
        // This would be implemented to filter products by price range
        console.log(`Filter by price: $${min} - $${max}`);
    }
    
    function addToWishlist(productId) {
        <?php if (isLoggedIn()): ?>
            fetch('/E-Commerce/api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            });
        <?php else: ?>
            showNotification('Please login to add items to wishlist', 'error');
            setTimeout(() => {
                window.location.href = '?page=login';
            }, 2000);
        <?php endif; ?>
    }
</script>
