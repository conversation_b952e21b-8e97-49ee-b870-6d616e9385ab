<?php
$pageTitle = 'Shopping Cart';
$cart = getCartItems();
$cartDetails = [];
$total = 0;

// Get cart details
foreach ($cart as $productId => $quantity) {
    $db->query("SELECT p.*, pi.image_url FROM products p 
               LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
               WHERE p.id = :id");
    $db->bind(':id', $productId);
    $product = $db->single();
    
    if ($product) {
        $price = $product['sale_price'] ?: $product['price'];
        $subtotal = $price * $quantity;
        $total += $subtotal;
        
        $cartDetails[] = [
            'product' => $product,
            'quantity' => $quantity,
            'price' => $price,
            'subtotal' => $subtotal
        ];
    }
}

$tax = $total * ($siteSettings['tax_rate'] ?? 0.10);
$shipping = $total > 50 ? 0 : 9.99; // Free shipping over $50
$grandTotal = $total + $tax + $shipping;
?>

<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?= SITE_URL ?>" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">Shopping Cart</span>
                </div>
            </li>
        </ol>
    </nav>

    <h1 class="text-3xl font-bold text-gray-800 mb-8">Shopping Cart</h1>

    <?php if (empty($cartDetails)): ?>
        <!-- Empty Cart -->
        <div class="text-center py-16">
            <i class="fas fa-shopping-cart text-gray-400 text-6xl mb-6"></i>
            <h2 class="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h2>
            <p class="text-gray-500 mb-8">Looks like you haven't added any items to your cart yet.</p>
            <a href="?page=products" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                Continue Shopping
            </a>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold">Cart Items (<?= count($cartDetails) ?>)</h2>
                    </div>
                    
                    <div class="divide-y divide-gray-200">
                        <?php foreach ($cartDetails as $item): ?>
                            <div class="p-6 flex items-center space-x-4" data-product-id="<?= $item['product']['id'] ?>">
                                <!-- Product Image -->
                                <div class="flex-shrink-0">
                                    <a href="?page=product&id=<?= $item['product']['id'] ?>">
                                        <?php if ($item['product']['image_url']): ?>
                                            <img src="<?= SITE_URL ?>/<?= $item['product']['image_url'] ?>" 
                                                 alt="<?= htmlspecialchars($item['product']['name']) ?>" 
                                                 class="w-20 h-20 object-cover rounded-lg">
                                        <?php else: ?>
                                            <div class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </a>
                                </div>
                                
                                <!-- Product Details -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-medium text-gray-900 truncate">
                                        <a href="?page=product&id=<?= $item['product']['id'] ?>" class="hover:text-blue-600">
                                            <?= htmlspecialchars($item['product']['name']) ?>
                                        </a>
                                    </h3>
                                    <p class="text-sm text-gray-500">SKU: <?= htmlspecialchars($item['product']['sku']) ?></p>
                                    
                                    <?php if ($item['product']['sale_price']): ?>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <span class="text-lg font-bold text-red-600">
                                                <?= formatPrice($item['product']['sale_price'], $siteSettings['currency']) ?>
                                            </span>
                                            <span class="text-sm text-gray-500 line-through">
                                                <?= formatPrice($item['product']['price'], $siteSettings['currency']) ?>
                                            </span>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-lg font-bold text-gray-900">
                                            <?= formatPrice($item['product']['price'], $siteSettings['currency']) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Quantity Controls -->
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center border border-gray-300 rounded-lg">
                                        <button onclick="updateQuantity(<?= $item['product']['id'] ?>, <?= $item['quantity'] - 1 ?>)" 
                                                class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-l-lg">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" 
                                               value="<?= $item['quantity'] ?>" 
                                               min="1" 
                                               max="<?= $item['product']['stock_quantity'] ?>"
                                               class="w-16 px-3 py-2 text-center border-0 focus:outline-none"
                                               onchange="updateQuantity(<?= $item['product']['id'] ?>, this.value)">
                                        <button onclick="updateQuantity(<?= $item['product']['id'] ?>, <?= $item['quantity'] + 1 ?>)" 
                                                class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-r-lg">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="text-lg font-semibold text-gray-900 min-w-0">
                                        <?= formatPrice($item['subtotal'], $siteSettings['currency']) ?>
                                    </div>
                                    
                                    <button onclick="removeFromCart(<?= $item['product']['id'] ?>)" 
                                            class="text-red-500 hover:text-red-700 p-2">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Cart Actions -->
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <div class="flex justify-between items-center">
                            <a href="?page=products" class="text-blue-600 hover:text-blue-800 font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>Continue Shopping
                            </a>
                            <button onclick="clearCart()" class="text-red-600 hover:text-red-800 font-medium">
                                <i class="fas fa-trash mr-2"></i>Clear Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                    <h2 class="text-lg font-semibold mb-4">Order Summary</h2>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-medium cart-total"><?= formatPrice($total, $siteSettings['currency']) ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tax</span>
                            <span class="font-medium"><?= formatPrice($tax, $siteSettings['currency']) ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Shipping</span>
                            <span class="font-medium">
                                <?php if ($shipping > 0): ?>
                                    <?= formatPrice($shipping, $siteSettings['currency']) ?>
                                <?php else: ?>
                                    <span class="text-green-600">Free</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        
                        <?php if ($shipping > 0): ?>
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i>
                                Free shipping on orders over $50
                            </div>
                        <?php endif; ?>
                        
                        <div class="border-t pt-3">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total</span>
                                <span><?= formatPrice($grandTotal, $siteSettings['currency']) ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Coupon Code -->
                    <?php if (getFeatureStatus('discount_coupons')): ?>
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Coupon Code</label>
                            <div class="flex">
                                <input type="text" id="couponCode" placeholder="Enter coupon code" 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:border-blue-500">
                                <button onclick="applyCoupon()" 
                                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-r-lg transition-colors">
                                    Apply
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Checkout Button -->
                    <div class="space-y-3">
                        <?php if (isLoggedIn()): ?>
                            <a href="?page=checkout" 
                               class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-3 px-4 rounded-lg font-semibold transition-colors">
                                Proceed to Checkout
                            </a>
                        <?php else: ?>
                            <a href="?page=login?redirect=checkout" 
                               class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-3 px-4 rounded-lg font-semibold transition-colors">
                                Login to Checkout
                            </a>
                            <p class="text-sm text-gray-500 text-center">
                                New customer? <a href="?page=register" class="text-blue-600 hover:text-blue-800">Create an account</a>
                            </p>
                        <?php endif; ?>
                        
                        <!-- Express Checkout Options -->
                        <div class="space-y-2">
                            <button class="w-full bg-yellow-400 hover:bg-yellow-500 text-black py-2 px-4 rounded-lg font-medium transition-colors">
                                <i class="fab fa-paypal mr-2"></i>PayPal Express
                            </button>
                            <button class="w-full bg-black hover:bg-gray-800 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                <i class="fab fa-apple-pay mr-2"></i>Apple Pay
                            </button>
                        </div>
                    </div>
                    
                    <!-- Security Badges -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-green-500 mr-1"></i>
                                Secure Checkout
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-lock text-green-500 mr-1"></i>
                                SSL Encrypted
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    function updateQuantity(productId, quantity) {
        if (quantity < 1) {
            removeFromCart(productId);
            return;
        }
        
        updateCartQuantity(productId, quantity);
    }
    
    function clearCart() {
        if (!confirm('Are you sure you want to clear your entire cart?')) {
            return;
        }
        
        showLoading();
        
        fetch('/E-Commerce/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'clear'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                location.reload();
            } else {
                showNotification('Error clearing cart: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('An error occurred. Please try again.', 'error');
        });
    }
    
    function applyCoupon() {
        const couponCode = document.getElementById('couponCode').value.trim();
        
        if (!couponCode) {
            showNotification('Please enter a coupon code', 'warning');
            return;
        }
        
        showLoading();
        
        fetch('/E-Commerce/api/coupon.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'apply',
                code: couponCode
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('Coupon applied successfully!', 'success');
                location.reload();
            } else {
                showNotification('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('An error occurred. Please try again.', 'error');
        });
    }
</script>
