<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?><?= htmlspecialchars($siteSettings['site_name']) ?></title>
    <meta name="description" content="<?= htmlspecialchars($siteSettings['site_description']) ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= SITE_URL ?>/themes/modern/assets/css/style.css">
    
    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <style>
        .theme-modern {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
        }
    </style>
</head>
<body class="theme-modern bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gray-800 text-white text-sm">
        <div class="container mx-auto px-4 py-2">
            <div class="flex justify-between items-center">
                <div class="flex space-x-4">
                    <span><i class="fas fa-phone mr-1"></i> <?= htmlspecialchars($siteSettings['contact_phone']) ?></span>
                    <span><i class="fas fa-envelope mr-1"></i> <?= htmlspecialchars($siteSettings['contact_email']) ?></span>
                </div>
                <div class="flex space-x-4">
                    <?php if (isLoggedIn()): ?>
                        <a href="?page=profile" class="hover:text-blue-300">My Account</a>
                        <a href="?page=orders" class="hover:text-blue-300">Orders</a>
                        <a href="api/logout.php" class="hover:text-blue-300">Logout</a>
                    <?php else: ?>
                        <a href="?page=login" class="hover:text-blue-300">Login</a>
                        <a href="?page=register" class="hover:text-blue-300">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?= SITE_URL ?>" class="text-2xl font-bold text-blue-600">
                        <?php if (!empty($siteSettings['logo'])): ?>
                            <img src="<?= SITE_URL ?>/<?= $siteSettings['logo'] ?>" alt="<?= htmlspecialchars($siteSettings['site_name']) ?>" class="h-10">
                        <?php else: ?>
                            <?= htmlspecialchars($siteSettings['site_name']) ?>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <form class="w-full" action="?page=products" method="GET">
                        <input type="hidden" name="page" value="products">
                        <div class="relative">
                            <input type="text" name="search" placeholder="Search products..." 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500">
                            <button type="submit" class="absolute right-2 top-2 text-gray-500 hover:text-blue-500">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Cart & User Actions -->
                <div class="flex items-center space-x-4">
                    <?php if (getFeatureStatus('wishlist')): ?>
                        <a href="?page=wishlist" class="text-gray-600 hover:text-blue-600 relative">
                            <i class="fas fa-heart text-xl"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                        </a>
                    <?php endif; ?>
                    
                    <a href="?page=cart" class="text-gray-600 hover:text-blue-600 relative">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span class="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center cart-count">
                            <?= count(getCartItems()) ?>
                        </span>
                    </a>
                    
                    <!-- Mobile Menu Button -->
                    <button class="md:hidden text-gray-600 hover:text-blue-600" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="hidden md:block border-t border-gray-200">
                <ul class="flex space-x-8 py-4">
                    <li><a href="<?= SITE_URL ?>" class="text-gray-700 hover:text-blue-600 font-medium">Home</a></li>
                    <li><a href="?page=products" class="text-gray-700 hover:text-blue-600 font-medium">Products</a></li>
                    
                    <!-- Categories Dropdown -->
                    <li class="relative group">
                        <a href="#" class="text-gray-700 hover:text-blue-600 font-medium flex items-center">
                            Categories <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </a>
                        <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <?php
                            // Get categories
                            $db->query("SELECT * FROM categories WHERE status = 'active' AND parent_id IS NULL ORDER BY sort_order, name");
                            $categories = $db->resultset();
                            foreach ($categories as $category):
                            ?>
                                <a href="?page=products&category=<?= $category['slug'] ?>" 
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <?= htmlspecialchars($category['name']) ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </li>
                    
                    <li><a href="?page=about" class="text-gray-700 hover:text-blue-600 font-medium">About</a></li>
                    <li><a href="?page=contact" class="text-gray-700 hover:text-blue-600 font-medium">Contact</a></li>
                </ul>
            </nav>

            <!-- Mobile Navigation -->
            <nav id="mobileMenu" class="md:hidden hidden border-t border-gray-200">
                <ul class="py-4 space-y-2">
                    <li><a href="<?= SITE_URL ?>" class="block py-2 text-gray-700 hover:text-blue-600">Home</a></li>
                    <li><a href="?page=products" class="block py-2 text-gray-700 hover:text-blue-600">Products</a></li>
                    <li><a href="?page=about" class="block py-2 text-gray-700 hover:text-blue-600">About</a></li>
                    <li><a href="?page=contact" class="block py-2 text-gray-700 hover:text-blue-600">Contact</a></li>
                    
                    <!-- Mobile Search -->
                    <li class="pt-4">
                        <form action="?page=products" method="GET">
                            <input type="hidden" name="page" value="products">
                            <div class="relative">
                                <input type="text" name="search" placeholder="Search products..." 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500">
                                <button type="submit" class="absolute right-2 top-2 text-gray-500 hover:text-blue-500">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen">
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mx-4 mt-4">
                <?= htmlspecialchars($_SESSION['success_message']) ?>
                <?php unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 mt-4">
                <?= htmlspecialchars($_SESSION['error_message']) ?>
                <?php unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }
        
        // Update cart count
        function updateCartCount() {
            fetch('api/cart-count.php')
                .then(response => response.json())
                .then(data => {
                    document.querySelector('.cart-count').textContent = data.count;
                });
        }
    </script>
