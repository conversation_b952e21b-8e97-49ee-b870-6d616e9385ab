-- Sample Data for E-Commerce Multi-Theme Platform
-- Run this after creating the main schema

USE ecommerce_multi;

-- Insert sample categories
INSERT INTO categories (name, slug, description, status, sort_order) VALUES
('Electronics', 'electronics', 'Latest electronic gadgets and devices', 'active', 1),
('Clothing', 'clothing', 'Fashion and apparel for all ages', 'active', 2),
('Books', 'books', 'Books, magazines, and educational materials', 'active', 3),
('Home & Garden', 'home-garden', 'Home improvement and garden supplies', 'active', 4),
('Sports', 'sports', 'Sports equipment and fitness gear', 'active', 5),
('Beauty', 'beauty', 'Beauty and personal care products', 'active', 6);

-- Insert sample products
INSERT INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, featured, status) VALUES
('Wireless Bluetooth Headphones', 'wireless-bluetooth-headphones', 'High-quality wireless headphones with noise cancellation and 30-hour battery life. Perfect for music lovers and professionals.', 'Premium wireless headphones with noise cancellation', 'WBH-001', 199.99, 149.99, 50, 1, 'active'),

('Smartphone Case', 'smartphone-case', 'Durable protective case for smartphones with shock absorption and wireless charging compatibility.', 'Protective smartphone case with wireless charging', 'SPC-002', 29.99, NULL, 100, 1, 'active'),

('Laptop Stand', 'laptop-stand', 'Ergonomic aluminum laptop stand that improves posture and increases productivity. Compatible with all laptop sizes.', 'Ergonomic aluminum laptop stand', 'LPS-003', 79.99, 59.99, 25, 1, 'active'),

('Cotton T-Shirt', 'cotton-t-shirt', 'Comfortable 100% organic cotton t-shirt available in multiple colors and sizes. Perfect for casual wear.', '100% organic cotton t-shirt', 'CTS-004', 24.99, NULL, 200, 1, 'active'),

('Running Shoes', 'running-shoes', 'Professional running shoes with advanced cushioning technology and breathable mesh upper. Ideal for athletes.', 'Professional running shoes with advanced cushioning', 'RS-005', 129.99, 99.99, 75, 1, 'active'),

('Coffee Maker', 'coffee-maker', 'Programmable coffee maker with 12-cup capacity and auto-shutoff feature. Makes perfect coffee every time.', 'Programmable 12-cup coffee maker', 'CM-006', 89.99, NULL, 30, 1, 'active'),

('Yoga Mat', 'yoga-mat', 'Non-slip yoga mat made from eco-friendly materials. Perfect for yoga, pilates, and fitness exercises.', 'Eco-friendly non-slip yoga mat', 'YM-007', 39.99, 29.99, 60, 1, 'active'),

('Skincare Set', 'skincare-set', 'Complete skincare routine set with cleanser, toner, serum, and moisturizer. Suitable for all skin types.', 'Complete skincare routine set', 'SS-008', 149.99, 119.99, 40, 1, 'active'),

('Bluetooth Speaker', 'bluetooth-speaker', 'Portable Bluetooth speaker with 360-degree sound and waterproof design. Perfect for outdoor activities.', 'Portable waterproof Bluetooth speaker', 'BS-009', 79.99, NULL, 80, 0, 'active'),

('Gaming Mouse', 'gaming-mouse', 'High-precision gaming mouse with customizable RGB lighting and programmable buttons. Perfect for gamers.', 'High-precision gaming mouse with RGB lighting', 'GM-010', 69.99, 49.99, 45, 0, 'active'),

('Desk Lamp', 'desk-lamp', 'LED desk lamp with adjustable brightness and color temperature. USB charging port included.', 'LED desk lamp with adjustable brightness', 'DL-011', 49.99, NULL, 35, 0, 'active'),

('Backpack', 'backpack', 'Durable travel backpack with multiple compartments and laptop sleeve. Perfect for work and travel.', 'Durable travel backpack with laptop sleeve', 'BP-012', 89.99, 69.99, 55, 0, 'active');

-- Link products to categories
INSERT INTO product_categories (product_id, category_id) VALUES
(1, 1), -- Headphones -> Electronics
(2, 1), -- Phone Case -> Electronics
(3, 1), -- Laptop Stand -> Electronics
(4, 2), -- T-Shirt -> Clothing
(5, 5), -- Running Shoes -> Sports
(6, 4), -- Coffee Maker -> Home & Garden
(7, 5), -- Yoga Mat -> Sports
(8, 6), -- Skincare Set -> Beauty
(9, 1), -- Bluetooth Speaker -> Electronics
(10, 1), -- Gaming Mouse -> Electronics
(11, 4), -- Desk Lamp -> Home & Garden
(12, 2); -- Backpack -> Clothing

-- Insert sample product images (placeholder URLs)
INSERT INTO product_images (product_id, image_url, alt_text, is_primary) VALUES
(1, 'uploads/products/headphones-1.jpg', 'Wireless Bluetooth Headphones', 1),
(2, 'uploads/products/phone-case-1.jpg', 'Smartphone Case', 1),
(3, 'uploads/products/laptop-stand-1.jpg', 'Laptop Stand', 1),
(4, 'uploads/products/t-shirt-1.jpg', 'Cotton T-Shirt', 1),
(5, 'uploads/products/running-shoes-1.jpg', 'Running Shoes', 1),
(6, 'uploads/products/coffee-maker-1.jpg', 'Coffee Maker', 1),
(7, 'uploads/products/yoga-mat-1.jpg', 'Yoga Mat', 1),
(8, 'uploads/products/skincare-set-1.jpg', 'Skincare Set', 1),
(9, 'uploads/products/bluetooth-speaker-1.jpg', 'Bluetooth Speaker', 1),
(10, 'uploads/products/gaming-mouse-1.jpg', 'Gaming Mouse', 1),
(11, 'uploads/products/desk-lamp-1.jpg', 'Desk Lamp', 1),
(12, 'uploads/products/backpack-1.jpg', 'Backpack', 1);

-- Insert sample product attributes
INSERT INTO product_attributes (name, slug, type) VALUES
('Color', 'color', 'select'),
('Size', 'size', 'select'),
('Material', 'material', 'text'),
('Brand', 'brand', 'text'),
('Weight', 'weight', 'text');

-- Insert sample attribute values
INSERT INTO product_attribute_values (product_id, attribute_id, value) VALUES
-- Headphones
(1, 1, 'Black'),
(1, 4, 'AudioTech'),
(1, 5, '250g'),

-- Phone Case
(2, 1, 'Clear'),
(2, 3, 'TPU'),
(2, 4, 'ProtectCase'),

-- T-Shirt
(4, 1, 'Blue'),
(4, 2, 'Medium'),
(4, 3, 'Organic Cotton'),
(4, 4, 'EcoWear'),

-- Running Shoes
(5, 1, 'Black/White'),
(5, 2, '10'),
(5, 4, 'RunFast'),
(5, 5, '300g'),

-- Yoga Mat
(7, 1, 'Purple'),
(7, 3, 'TPE'),
(7, 4, 'ZenFit'),
(7, 5, '1.2kg');

-- Insert sample admin user
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', 'active', 1);
-- Password is 'password' (hashed)

-- Insert sample customer users
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES
('john_doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', 'customer', 'active', 1),
('jane_smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', 'customer', 'active', 1);

-- Insert sample reviews
INSERT INTO reviews (product_id, user_id, rating, title, content, status) VALUES
(1, 2, 5, 'Excellent headphones!', 'These headphones are amazing. Great sound quality and battery life.', 'approved'),
(1, 3, 4, 'Good value for money', 'Nice headphones, comfortable to wear for long periods.', 'approved'),
(4, 2, 5, 'Perfect fit', 'Love this t-shirt! Very comfortable and good quality cotton.', 'approved'),
(5, 3, 5, 'Best running shoes ever', 'These shoes are incredibly comfortable for running. Highly recommended!', 'approved'),
(7, 2, 4, 'Great yoga mat', 'Good quality mat, non-slip surface works well.', 'approved');

-- Insert sample coupons
INSERT INTO coupons (code, description, discount_type, discount_value, minimum_spend, usage_limit, start_date, end_date, status) VALUES
('WELCOME10', 'Welcome discount for new customers', 'percentage', 10.00, 50.00, 100, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'active'),
('SAVE20', 'Save $20 on orders over $100', 'fixed_amount', 20.00, 100.00, 50, NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY), 'active'),
('SUMMER15', 'Summer sale - 15% off', 'percentage', 15.00, 75.00, 200, NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 'active');

-- Insert sample orders
INSERT INTO orders (user_id, order_number, status, total_amount, subtotal, tax_amount, shipping_amount, currency, payment_status, payment_method, billing_address, shipping_address) VALUES
(2, 'ORD-001', 'delivered', 179.98, 149.99, 15.00, 14.99, 'USD', 'paid', 'stripe', 
'{"first_name":"John","last_name":"Doe","address_line_1":"123 Main St","city":"New York","state":"NY","postal_code":"10001","country":"USA"}',
'{"first_name":"John","last_name":"Doe","address_line_1":"123 Main St","city":"New York","state":"NY","postal_code":"10001","country":"USA"}'),

(3, 'ORD-002', 'processing', 154.97, 129.98, 13.00, 11.99, 'USD', 'paid', 'paypal',
'{"first_name":"Jane","last_name":"Smith","address_line_1":"456 Oak Ave","city":"Los Angeles","state":"CA","postal_code":"90210","country":"USA"}',
'{"first_name":"Jane","last_name":"Smith","address_line_1":"456 Oak Ave","city":"Los Angeles","state":"CA","postal_code":"90210","country":"USA"}');

-- Insert sample order items
INSERT INTO order_items (order_id, product_id, quantity, price, total, product_name, product_sku) VALUES
(1, 1, 1, 149.99, 149.99, 'Wireless Bluetooth Headphones', 'WBH-001'),
(2, 5, 1, 99.99, 99.99, 'Running Shoes', 'RS-005'),
(2, 7, 1, 29.99, 29.99, 'Yoga Mat', 'YM-007');

-- Update site settings with sample data
UPDATE site_settings SET 
    site_name = 'TechStore Pro',
    site_description = 'Your one-stop shop for electronics, fashion, and lifestyle products',
    contact_email = '<EMAIL>',
    contact_phone = '+****************',
    address = '123 Commerce Street, Tech City, TC 12345, USA',
    currency = 'USD',
    tax_rate = 0.10,
    social_facebook = 'https://facebook.com/techstore',
    social_twitter = 'https://twitter.com/techstore',
    social_instagram = 'https://instagram.com/techstore',
    footer_text = 'Quality products, fast shipping, excellent customer service.'
WHERE id = 1;

-- Create placeholder image directories (you would need to add actual images)
-- uploads/products/
-- uploads/categories/
-- uploads/logos/

COMMIT;
