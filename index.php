<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get current theme
$theme = getCurrentTheme();
$siteSettings = getSiteSettings();

// Route handling
$page = $_GET['page'] ?? 'home';
$allowedPages = ['home', 'products', 'product', 'cart', 'checkout', 'login', 'register', 'profile', 'orders'];

if (!in_array($page, $allowedPages)) {
    $page = 'home';
}

// Load theme header
include "themes/{$theme}/header.php";

// Load page content
switch($page) {
    case 'home':
        include "themes/{$theme}/pages/home.php";
        break;
    case 'products':
        include "themes/{$theme}/pages/products.php";
        break;
    case 'product':
        include "themes/{$theme}/pages/product-detail.php";
        break;
    case 'cart':
        include "themes/{$theme}/pages/cart.php";
        break;
    case 'checkout':
        include "themes/{$theme}/pages/checkout.php";
        break;
    case 'login':
        include "themes/{$theme}/pages/login.php";
        break;
    case 'register':
        include "themes/{$theme}/pages/register.php";
        break;
    case 'profile':
        include "themes/{$theme}/pages/profile.php";
        break;
    case 'orders':
        include "themes/{$theme}/pages/orders.php";
        break;
    default:
        include "themes/{$theme}/pages/home.php";
}

// Load theme footer
include "themes/{$theme}/footer.php";
?>
