# Multi-Theme E-Commerce Platform

A fully functional, secure, and scalable e-commerce website with a developer settings panel for easy theme management and customization. Built with PHP, MySQL, HTML, CSS, JavaScript, and Tailwind CSS.

## Features

### Core E-Commerce Features
- ✅ Product catalog with categories
- ✅ Shopping cart functionality
- ✅ User registration and authentication
- ✅ Order management system
- ✅ Product search and filtering
- ✅ Responsive design
- ✅ Secure payment processing (Stripe/PayPal ready)
- ✅ Inventory management
- ✅ User reviews and ratings
- ✅ Wishlist functionality
- ✅ Discount coupons
- ✅ Newsletter subscription

### Multi-Theme System
- 🎨 **10 Modern Themes** (Currently implementing Modern theme)
  - Modern Minimalist
  - Classic Business
  - Trendy Fashion
  - Tech Store
  - Organic Natural
  - Luxury Premium
  - Sports & Fitness
  - Books & Media
  - Electronics Hub
  - Multi-vendor Marketplace

### Developer Settings Panel
- ⚙️ Theme switching with live preview
- 🎛️ Website settings management (name, logo, contact info)
- 🔧 Feature toggles (enable/disable components)
- 📊 Business customization options
- 🗑️ Production deployment (removes dev panel)

### Security Features
- 🔒 SQL injection prevention
- 🛡️ CSRF protection
- 🔐 Password hashing
- 🚫 Input validation and sanitization
- 👤 Role-based access control

## Installation

### Prerequisites
- XAMPP/WAMP/LAMP server
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   # Place the project in your XAMPP htdocs directory
   # Path should be: C:\xampp\htdocs\E-Commerce
   ```

2. **Database Setup**
   - Start XAMPP and ensure Apache and MySQL are running
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Import the database schema:
     ```sql
     # Run the SQL file: database/schema.sql
     ```

3. **Configuration**
   - Edit `config/config.php` and update:
     - Database credentials
     - Site URL
     - Payment gateway credentials (optional)
     - Email settings (optional)

4. **File Permissions**
   - Ensure the `uploads/` directory is writable
   - Set appropriate permissions for file uploads

5. **Access the Website**
   - Frontend: http://localhost/E-Commerce
   - Developer Panel: http://localhost/E-Commerce/dev-admin
   - Default dev panel password: `dev123admin`

## Project Structure

```
E-Commerce/
├── api/                    # API endpoints
│   ├── cart.php           # Shopping cart API
│   ├── cart-count.php     # Cart count API
│   └── ...
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database connection
├── database/              # Database files
│   └── schema.sql         # Database schema
├── dev-admin/             # Developer settings panel
│   └── index.php          # Admin interface
├── includes/              # Shared PHP functions
│   └── functions.php      # Core functions
├── themes/                # Theme system
│   └── modern/            # Modern theme
│       ├── assets/        # Theme assets
│       ├── pages/         # Theme pages
│       ├── header.php     # Theme header
│       └── footer.php     # Theme footer
├── uploads/               # File uploads directory
├── index.php              # Main entry point
└── README.md              # This file
```

## Theme Development

### Creating a New Theme

1. **Create theme directory**
   ```
   themes/your-theme-name/
   ├── assets/
   │   ├── css/
   │   ├── js/
   │   └── images/
   ├── pages/
   ├── header.php
   └── footer.php
   ```

2. **Add theme to configuration**
   ```php
   // In config/config.php
   define('AVAILABLE_THEMES', [
       'your-theme-name' => 'Your Theme Display Name',
       // ... other themes
   ]);
   ```

3. **Implement required files**
   - `header.php` - Theme header and navigation
   - `footer.php` - Theme footer
   - `pages/home.php` - Homepage
   - `pages/products.php` - Products listing
   - `pages/product-detail.php` - Product details
   - `pages/cart.php` - Shopping cart
   - `pages/checkout.php` - Checkout process

### Theme Guidelines

- Use Tailwind CSS for styling consistency
- Implement responsive design (mobile-first)
- Follow accessibility best practices
- Include proper SEO meta tags
- Maintain consistent component structure

## Developer Panel Features

### Theme Management
- Switch between available themes instantly
- Preview themes before applying
- Theme-specific customization options

### Site Settings
- Business information (name, description, contact)
- Logo and branding management
- Social media links
- Currency and tax settings

### Feature Toggles
- User reviews: Enable/disable product reviews
- Wishlist: Toggle wishlist functionality
- Live chat: Enable customer support chat
- Newsletter: Toggle newsletter subscription
- Social login: Enable social media authentication
- Multi-currency: Support multiple currencies
- Inventory tracking: Enable stock management
- Discount coupons: Toggle coupon system
- Product recommendations: Enable AI recommendations

### Production Deployment

Before deploying to production:

1. **Remove developer panel**
   ```bash
   # Delete the dev-admin directory
   rm -rf dev-admin/
   ```

2. **Update configuration**
   ```php
   // In config/config.php
   // Disable error reporting
   error_reporting(0);
   ini_set('display_errors', 0);
   
   // Update database credentials
   // Update site URL
   // Add production payment credentials
   ```

3. **Security checklist**
   - Change default passwords
   - Enable HTTPS
   - Configure proper file permissions
   - Set up regular backups
   - Enable security headers

## API Documentation

### Cart API (`api/cart.php`)
- `POST` with `action: 'add'` - Add product to cart
- `POST` with `action: 'update'` - Update cart quantity
- `POST` with `action: 'remove'` - Remove from cart
- `POST` with `action: 'clear'` - Clear entire cart
- `POST` with `action: 'get'` - Get cart contents

### Response Format
```json
{
    "success": true,
    "message": "Product added to cart",
    "cart_count": 3,
    "cart_total": "$45.99"
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments

## Roadmap

- [ ] Complete all 10 themes
- [ ] Advanced analytics dashboard
- [ ] Multi-vendor marketplace features
- [ ] Mobile app API
- [ ] Advanced SEO tools
- [ ] Inventory forecasting
- [ ] Customer loyalty program
- [ ] Advanced reporting system

---

**Note**: This is a development version. Always test thoroughly before deploying to production and remove the `dev-admin` directory for security.
