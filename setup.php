<?php
/**
 * E-Commerce Multi-Theme Platform Setup Script
 * Run this file once to set up the database and initial configuration
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Installation already completed. Delete config/installed.lock to reinstall.');
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Database configuration
            $dbHost = $_POST['db_host'] ?? 'localhost';
            $dbUser = $_POST['db_user'] ?? 'root';
            $dbPass = $_POST['db_pass'] ?? '';
            $dbName = $_POST['db_name'] ?? 'ecommerce_multi';
            
            // Test database connection
            try {
                $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database if it doesn't exist
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$dbName`");
                
                // Update config file
                $configContent = file_get_contents('config/config.php');
                $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$dbHost');", $configContent);
                $configContent = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$dbUser');", $configContent);
                $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$dbPass');", $configContent);
                $configContent = str_replace("define('DB_NAME', 'ecommerce_multi');", "define('DB_NAME', '$dbName');", $configContent);
                
                file_put_contents('config/config.php', $configContent);
                
                $success[] = 'Database connection successful!';
                $step = 2;
            } catch (PDOException $e) {
                $errors[] = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 2:
            // Import database schema
            try {
                require_once 'config/config.php';
                $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Read and execute schema
                $schema = file_get_contents('database/schema.sql');
                $statements = explode(';', $schema);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // Import sample data if requested
                if (isset($_POST['import_sample_data'])) {
                    $sampleData = file_get_contents('database/sample-data.sql');
                    $statements = explode(';', $sampleData);
                    
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement) && !preg_match('/^(USE|COMMIT)/i', $statement)) {
                            $pdo->exec($statement);
                        }
                    }
                    $success[] = 'Sample data imported successfully!';
                }
                
                $success[] = 'Database schema created successfully!';
                $step = 3;
            } catch (PDOException $e) {
                $errors[] = 'Database setup failed: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Site configuration
            $siteUrl = rtrim($_POST['site_url'] ?? 'http://localhost/E-Commerce', '/');
            $siteName = $_POST['site_name'] ?? 'E-Commerce Store';
            $adminEmail = $_POST['admin_email'] ?? '<EMAIL>';
            
            // Update config file
            $configContent = file_get_contents('config/config.php');
            $configContent = str_replace("define('SITE_URL', 'http://localhost/E-Commerce');", "define('SITE_URL', '$siteUrl');", $configContent);
            $configContent = str_replace("define('ADMIN_EMAIL', '<EMAIL>');", "define('ADMIN_EMAIL', '$adminEmail');", $configContent);
            
            file_put_contents('config/config.php', $configContent);
            
            // Update site settings in database
            try {
                require_once 'config/config.php';
                require_once 'config/database.php';
                
                $db->query("UPDATE site_settings SET site_name = :name, contact_email = :email WHERE id = 1");
                $db->bind(':name', $siteName);
                $db->bind(':email', $adminEmail);
                $db->execute();
                
                $success[] = 'Site configuration updated successfully!';
                $step = 4;
            } catch (Exception $e) {
                $errors[] = 'Configuration update failed: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // Create directories and set permissions
            $directories = [
                'uploads',
                'uploads/products',
                'uploads/categories',
                'uploads/logos',
                'uploads/temp'
            ];
            
            foreach ($directories as $dir) {
                if (!file_exists($dir)) {
                    if (mkdir($dir, 0755, true)) {
                        $success[] = "Created directory: $dir";
                    } else {
                        $errors[] = "Failed to create directory: $dir";
                    }
                } else {
                    $success[] = "Directory already exists: $dir";
                }
            }
            
            // Create installation lock file
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            
            $step = 5;
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Setup</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h1 class="text-3xl font-bold text-center mb-8">E-Commerce Platform Setup</h1>
                
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium">Step <?= $step ?> of 5</span>
                        <span class="text-sm text-gray-500"><?= round(($step / 5) * 100) ?>% Complete</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <?= ($step / 5) * 100 ?>%"></div>
                    </div>
                </div>
                
                <!-- Messages -->
                <?php if (!empty($errors)): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <?php foreach ($errors as $error): ?>
                            <p><?= htmlspecialchars($error) ?></p>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        <?php foreach ($success as $message): ?>
                            <p><?= htmlspecialchars($message) ?></p>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <!-- Step 1: Database Configuration -->
                    <h2 class="text-xl font-semibold mb-4">Database Configuration</h2>
                    <form method="POST">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Database Host</label>
                                <input type="text" name="db_host" value="localhost" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Database Username</label>
                                <input type="text" name="db_user" value="root" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Database Password</label>
                                <input type="password" name="db_pass"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                                <input type="text" name="db_name" value="ecommerce_multi" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                        </div>
                        
                        <button type="submit" class="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                            Test Connection & Continue
                        </button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Database Setup -->
                    <h2 class="text-xl font-semibold mb-4">Database Setup</h2>
                    <p class="text-gray-600 mb-4">Create database tables and import initial data.</p>
                    
                    <form method="POST">
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="import_sample_data" checked class="mr-2">
                                <span class="text-sm">Import sample data (recommended for testing)</span>
                            </label>
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                            Create Database Tables
                        </button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Site Configuration -->
                    <h2 class="text-xl font-semibold mb-4">Site Configuration</h2>
                    <form method="POST">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Site URL</label>
                                <input type="url" name="site_url" value="http://localhost/E-Commerce" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                                <input type="text" name="site_name" value="E-Commerce Store" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Admin Email</label>
                                <input type="email" name="admin_email" value="<EMAIL>" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500">
                            </div>
                        </div>
                        
                        <button type="submit" class="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                            Save Configuration
                        </button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <!-- Step 4: File System Setup -->
                    <h2 class="text-xl font-semibold mb-4">File System Setup</h2>
                    <p class="text-gray-600 mb-4">Create necessary directories and set permissions.</p>
                    
                    <form method="POST">
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                            Create Directories
                        </button>
                    </form>
                    
                <?php elseif ($step == 5): ?>
                    <!-- Step 5: Complete -->
                    <div class="text-center">
                        <div class="mb-6">
                            <i class="fas fa-check-circle text-green-500 text-6xl mb-4"></i>
                            <h2 class="text-2xl font-semibold text-green-600 mb-2">Installation Complete!</h2>
                            <p class="text-gray-600">Your e-commerce platform is ready to use.</p>
                        </div>
                        
                        <div class="space-y-4">
                            <a href="index.php" class="block w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                                Visit Your Store
                            </a>
                            
                            <a href="dev-admin/" class="block w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors">
                                Access Developer Panel
                            </a>
                        </div>
                        
                        <div class="mt-8 p-4 bg-yellow-100 border border-yellow-400 rounded-md">
                            <h3 class="font-semibold text-yellow-800 mb-2">Important Notes:</h3>
                            <ul class="text-sm text-yellow-700 text-left space-y-1">
                                <li>• Default admin login: <EMAIL> / password</li>
                                <li>• Developer panel password: dev123admin</li>
                                <li>• Delete setup.php for security</li>
                                <li>• Remove dev-admin/ before production deployment</li>
                                <li>• Configure payment gateways in config/config.php</li>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
