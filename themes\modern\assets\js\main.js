// Modern Theme JavaScript

// Global variables
let cart = [];
let wishlist = [];

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    loadCart();
    setupEventListeners();
});

// Initialize theme
function initializeTheme() {
    // Add loading class to body
    document.body.classList.add('loading');
    
    // Remove loading class after page load
    window.addEventListener('load', function() {
        document.body.classList.remove('loading');
    });
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize lazy loading
    initializeLazyLoading();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
}

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('[onclick="toggleMobileMenu()"]');
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // Search form
    const searchForms = document.querySelectorAll('form[action*="products"]');
    searchForms.forEach(form => {
        form.addEventListener('submit', handleSearch);
    });
    
    // Add to cart buttons
    const addToCartBtns = document.querySelectorAll('[onclick*="addToCart"]');
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-product-id') || 
                             this.getAttribute('onclick').match(/addToCart\((\d+)\)/)[1];
            addToCart(parseInt(productId));
        });
    });
    
    // Quantity inputs
    const quantityInputs = document.querySelectorAll('input[type="number"][name="quantity"]');
    quantityInputs.forEach(input => {
        input.addEventListener('change', handleQuantityChange);
    });
    
    // Newsletter forms
    const newsletterForms = document.querySelectorAll('form[onsubmit*="subscribeNewsletter"]');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', subscribeNewsletter);
    });
}

// Mobile menu toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Handle search
function handleSearch(e) {
    const searchInput = e.target.querySelector('input[name="search"]');
    if (searchInput && searchInput.value.trim() === '') {
        e.preventDefault();
        showNotification('Please enter a search term', 'warning');
    }
}

// Add to cart function
async function addToCart(productId, quantity = 1) {
    if (!productId) {
        showNotification('Invalid product', 'error');
        return;
    }
    
    showLoading();
    
    try {
        const response = await fetch('/E-Commerce/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add',
                product_id: productId,
                quantity: quantity
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartCount(data.cart_count);
            showNotification('Product added to cart!', 'success');
            
            // Add animation to cart icon
            animateCartIcon();
        } else {
            showNotification(data.message || 'Failed to add product to cart', 'error');
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        hideLoading();
    }
}

// Update cart quantity
async function updateCartQuantity(productId, quantity) {
    showLoading();
    
    try {
        const response = await fetch('/E-Commerce/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update',
                product_id: productId,
                quantity: quantity
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartCount(data.cart_count);
            updateCartTotal(data.cart_total);
            showNotification('Cart updated!', 'success');
        } else {
            showNotification(data.message || 'Failed to update cart', 'error');
        }
    } catch (error) {
        console.error('Error updating cart:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        hideLoading();
    }
}

// Remove from cart
async function removeFromCart(productId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    showLoading();
    
    try {
        const response = await fetch('/E-Commerce/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'remove',
                product_id: productId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCartCount(data.cart_count);
            updateCartTotal(data.cart_total);
            showNotification('Product removed from cart', 'success');
            
            // Remove the item row if on cart page
            const itemRow = document.querySelector(`[data-product-id="${productId}"]`);
            if (itemRow) {
                itemRow.remove();
            }
        } else {
            showNotification(data.message || 'Failed to remove product', 'error');
        }
    } catch (error) {
        console.error('Error removing from cart:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        hideLoading();
    }
}

// Load cart data
async function loadCart() {
    try {
        const response = await fetch('/E-Commerce/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            cart = data.items;
            updateCartCount(data.count);
            updateCartTotal(data.formatted_total);
        }
    } catch (error) {
        console.error('Error loading cart:', error);
    }
}

// Update cart count in UI
function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count || 0;
    });
}

// Update cart total in UI
function updateCartTotal(total) {
    const cartTotalElements = document.querySelectorAll('.cart-total');
    cartTotalElements.forEach(element => {
        element.textContent = total || '$0.00';
    });
}

// Animate cart icon
function animateCartIcon() {
    const cartIcon = document.querySelector('.fa-shopping-cart');
    if (cartIcon) {
        cartIcon.classList.add('animate-bounce');
        setTimeout(() => {
            cartIcon.classList.remove('animate-bounce');
        }, 1000);
    }
}

// Handle quantity change
function handleQuantityChange(e) {
    const productId = e.target.getAttribute('data-product-id');
    const quantity = parseInt(e.target.value);
    
    if (productId && quantity >= 0) {
        updateCartQuantity(parseInt(productId), quantity);
    }
}

// Newsletter subscription
async function subscribeNewsletter(e) {
    e.preventDefault();
    
    const form = e.target;
    const email = form.querySelector('input[type="email"]').value;
    
    if (!email) {
        showNotification('Please enter your email address', 'warning');
        return;
    }
    
    showLoading();
    
    try {
        const response = await fetch('/E-Commerce/api/newsletter.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: email })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('Thank you for subscribing!', 'success');
            form.reset();
        } else {
            showNotification(data.message || 'Subscription failed', 'error');
        }
    } catch (error) {
        console.error('Error subscribing to newsletter:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        hideLoading();
    }
}

// Show loading overlay
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('hidden');
    }
}

// Hide loading overlay
function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

// Show notification
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, duration);
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// Show tooltip
function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

// Hide tooltip
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Initialize lazy loading
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// Initialize smooth scrolling
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Export functions for global use
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateCartQuantity = updateCartQuantity;
window.subscribeNewsletter = subscribeNewsletter;
window.showNotification = showNotification;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
