<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Simple authentication for dev panel (change this password!)
$DEV_PASSWORD = 'dev123admin';

if (!isset($_SESSION['dev_authenticated'])) {
    if ($_POST['dev_password'] ?? '' === $DEV_PASSWORD) {
        $_SESSION['dev_authenticated'] = true;
    } else {
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Developer Panel - Authentication</title>
            <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-md w-96">
                <h1 class="text-2xl font-bold mb-6 text-center">Developer Panel</h1>
                <form method="POST">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password:</label>
                        <input type="password" name="dev_password" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600">Access Panel</button>
                </form>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'update_theme':
            $theme = sanitizeInput($_POST['theme']);
            if (array_key_exists($theme, AVAILABLE_THEMES)) {
                $db->query("UPDATE site_settings SET theme_name = :theme WHERE id = 1");
                $db->bind(':theme', $theme);
                $db->execute();
                echo json_encode(['success' => true, 'message' => 'Theme updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid theme']);
            }
            exit;
            
        case 'update_settings':
            $settings = [
                'site_name' => sanitizeInput($_POST['site_name']),
                'site_description' => sanitizeInput($_POST['site_description']),
                'contact_email' => sanitizeInput($_POST['contact_email']),
                'contact_phone' => sanitizeInput($_POST['contact_phone']),
                'address' => sanitizeInput($_POST['address']),
                'currency' => sanitizeInput($_POST['currency']),
                'tax_rate' => floatval($_POST['tax_rate']),
                'social_facebook' => sanitizeInput($_POST['social_facebook']),
                'social_twitter' => sanitizeInput($_POST['social_twitter']),
                'social_instagram' => sanitizeInput($_POST['social_instagram']),
                'footer_text' => sanitizeInput($_POST['footer_text'])
            ];
            
            $db->query("UPDATE site_settings SET 
                site_name = :site_name,
                site_description = :site_description,
                contact_email = :contact_email,
                contact_phone = :contact_phone,
                address = :address,
                currency = :currency,
                tax_rate = :tax_rate,
                social_facebook = :social_facebook,
                social_twitter = :social_twitter,
                social_instagram = :social_instagram,
                footer_text = :footer_text
                WHERE id = 1");
                
            foreach ($settings as $key => $value) {
                $db->bind(':' . $key, $value);
            }
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'Settings updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update settings']);
            }
            exit;
            
        case 'toggle_feature':
            $feature = sanitizeInput($_POST['feature']);
            $status = $_POST['status'] === 'true' ? 1 : 0;
            
            $db->query("UPDATE feature_toggles SET status = :status WHERE feature_name = :feature");
            $db->bind(':status', $status);
            $db->bind(':feature', $feature);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'Feature updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update feature']);
            }
            exit;
    }
}

// Get current settings
$siteSettings = getSiteSettings();
$currentTheme = getCurrentTheme();

// Get feature toggles
$db->query("SELECT * FROM feature_toggles ORDER BY display_name");
$features = $db->resultset();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Developer Panel - E-Commerce Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Warning:</strong> This is the developer panel. Remove this directory before deploying to production!
        </div>
        
        <h1 class="text-3xl font-bold mb-8">E-Commerce Developer Panel</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Theme Selection -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Theme Selection</h2>
                <div class="grid grid-cols-2 gap-4">
                    <?php foreach (AVAILABLE_THEMES as $themeKey => $themeName): ?>
                        <div class="border rounded-lg p-4 cursor-pointer theme-option <?= $currentTheme === $themeKey ? 'border-blue-500 bg-blue-50' : 'border-gray-200' ?>" 
                             data-theme="<?= $themeKey ?>">
                            <h3 class="font-medium"><?= $themeName ?></h3>
                            <p class="text-sm text-gray-600">Click to preview</p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <button id="applyTheme" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Apply Selected Theme
                </button>
            </div>
            
            <!-- Site Settings -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Site Settings</h2>
                <form id="settingsForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Site Name</label>
                        <input type="text" name="site_name" value="<?= htmlspecialchars($siteSettings['site_name']) ?>" 
                               class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Site Description</label>
                        <textarea name="site_description" rows="3" 
                                  class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"><?= htmlspecialchars($siteSettings['site_description']) ?></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Contact Email</label>
                        <input type="email" name="contact_email" value="<?= htmlspecialchars($siteSettings['contact_email']) ?>" 
                               class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Contact Phone</label>
                        <input type="text" name="contact_phone" value="<?= htmlspecialchars($siteSettings['contact_phone']) ?>" 
                               class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Address</label>
                        <textarea name="address" rows="2" 
                                  class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"><?= htmlspecialchars($siteSettings['address']) ?></textarea>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Currency</label>
                            <select name="currency" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="USD" <?= $siteSettings['currency'] === 'USD' ? 'selected' : '' ?>>USD</option>
                                <option value="EUR" <?= $siteSettings['currency'] === 'EUR' ? 'selected' : '' ?>>EUR</option>
                                <option value="GBP" <?= $siteSettings['currency'] === 'GBP' ? 'selected' : '' ?>>GBP</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                            <input type="number" name="tax_rate" step="0.01" value="<?= $siteSettings['tax_rate'] * 100 ?>" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>
                    </div>
                    
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Update Settings
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Feature Toggles -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-8">
            <h2 class="text-xl font-semibold mb-4">Feature Toggles</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($features as $feature): ?>
                    <div class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium"><?= htmlspecialchars($feature['display_name']) ?></h3>
                                <p class="text-sm text-gray-600"><?= htmlspecialchars($feature['description']) ?></p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer feature-toggle" 
                                       data-feature="<?= $feature['feature_name'] ?>"
                                       <?= $feature['status'] ? 'checked' : '' ?>>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-8">
            <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
            <div class="flex space-x-4">
                <a href="../" target="_blank" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    View Website
                </a>
                <button onclick="exportSettings()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Export Settings
                </button>
                <button onclick="clearCache()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    Clear Cache
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedTheme = '<?= $currentTheme ?>';
        
        // Theme selection
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.theme-option').forEach(opt => {
                    opt.classList.remove('border-blue-500', 'bg-blue-50');
                    opt.classList.add('border-gray-200');
                });
                
                this.classList.remove('border-gray-200');
                this.classList.add('border-blue-500', 'bg-blue-50');
                selectedTheme = this.dataset.theme;
            });
        });
        
        // Apply theme
        document.getElementById('applyTheme').addEventListener('click', function() {
            const formData = new FormData();
            formData.append('action', 'update_theme');
            formData.append('theme', selectedTheme);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Theme updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
        
        // Settings form
        document.getElementById('settingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'update_settings');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Settings updated successfully!');
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });
        
        // Feature toggles
        document.querySelectorAll('.feature-toggle').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const formData = new FormData();
                formData.append('action', 'toggle_feature');
                formData.append('feature', this.dataset.feature);
                formData.append('status', this.checked);
                
                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        alert('Error: ' + data.message);
                        this.checked = !this.checked;
                    }
                });
            });
        });
        
        function exportSettings() {
            alert('Export functionality would be implemented here');
        }
        
        function clearCache() {
            alert('Cache cleared successfully!');
        }
    </script>
</body>
</html>
