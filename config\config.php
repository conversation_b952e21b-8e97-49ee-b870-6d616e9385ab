<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'ecommerce_multi');

// Site Configuration
define('SITE_URL', 'http://localhost/E-Commerce');
define('ADMIN_EMAIL', '<EMAIL>');
define('DEFAULT_THEME', 'modern');

// Security Configuration
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Payment Configuration (Add your payment gateway credentials)
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('PAYPAL_CLIENT_ID', 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', 'your_paypal_client_secret');

// File Upload Configuration
define('MAX_FILE_SIZE', 5242880); // 5MB
define('UPLOAD_PATH', 'uploads/');
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Theme Configuration
define('AVAILABLE_THEMES', [
    'modern' => 'Modern Minimalist',
    'classic' => 'Classic Business',
    'trendy' => 'Trendy Fashion',
    'tech' => 'Tech Store',
    'organic' => 'Organic Natural',
    'luxury' => 'Luxury Premium',
    'sports' => 'Sports & Fitness',
    'books' => 'Books & Media',
    'electronics' => 'Electronics Hub',
    'marketplace' => 'Multi-vendor Marketplace'
]);

// Feature Toggles (can be controlled from admin panel)
define('FEATURES', [
    'user_reviews' => true,
    'wishlist' => true,
    'compare_products' => true,
    'live_chat' => true,
    'newsletter' => true,
    'social_login' => true,
    'multi_currency' => false,
    'inventory_tracking' => true,
    'discount_coupons' => true,
    'product_recommendations' => true
]);

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');
?>
