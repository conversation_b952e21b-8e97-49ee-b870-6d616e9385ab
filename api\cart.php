<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

switch ($action) {
    case 'add':
        $productId = intval($input['product_id'] ?? 0);
        $quantity = intval($input['quantity'] ?? 1);
        
        if ($productId <= 0 || $quantity <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid product or quantity']);
            exit;
        }
        
        // Check if product exists and is available
        $db->query("SELECT * FROM products WHERE id = :id AND status = 'active'");
        $db->bind(':id', $productId);
        $product = $db->single();
        
        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }
        
        // Check stock
        if ($product['manage_stock'] && $product['stock_quantity'] < $quantity) {
            echo json_encode(['success' => false, 'message' => 'Insufficient stock']);
            exit;
        }
        
        // Add to cart
        addToCart($productId, $quantity);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Product added to cart',
            'cart_count' => count(getCartItems())
        ]);
        break;
        
    case 'update':
        $productId = intval($input['product_id'] ?? 0);
        $quantity = intval($input['quantity'] ?? 0);
        
        if ($productId <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid product']);
            exit;
        }
        
        if ($quantity <= 0) {
            removeFromCart($productId);
        } else {
            // Check stock
            $db->query("SELECT * FROM products WHERE id = :id AND status = 'active'");
            $db->bind(':id', $productId);
            $product = $db->single();
            
            if ($product && $product['manage_stock'] && $product['stock_quantity'] < $quantity) {
                echo json_encode(['success' => false, 'message' => 'Insufficient stock']);
                exit;
            }
            
            $_SESSION['cart'][$productId] = $quantity;
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'Cart updated',
            'cart_count' => count(getCartItems()),
            'cart_total' => formatPrice(getCartTotal(), getSiteSettings()['currency'])
        ]);
        break;
        
    case 'remove':
        $productId = intval($input['product_id'] ?? 0);
        
        if ($productId <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid product']);
            exit;
        }
        
        removeFromCart($productId);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Product removed from cart',
            'cart_count' => count(getCartItems()),
            'cart_total' => formatPrice(getCartTotal(), getSiteSettings()['currency'])
        ]);
        break;
        
    case 'clear':
        $_SESSION['cart'] = [];
        
        echo json_encode([
            'success' => true, 
            'message' => 'Cart cleared',
            'cart_count' => 0,
            'cart_total' => formatPrice(0, getSiteSettings()['currency'])
        ]);
        break;
        
    case 'get':
        $cart = getCartItems();
        $cartDetails = [];
        $total = 0;
        
        foreach ($cart as $productId => $quantity) {
            $db->query("SELECT p.*, pi.image_url FROM products p 
                       LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
                       WHERE p.id = :id");
            $db->bind(':id', $productId);
            $product = $db->single();
            
            if ($product) {
                $price = $product['sale_price'] ?: $product['price'];
                $subtotal = $price * $quantity;
                $total += $subtotal;
                
                $cartDetails[] = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'price' => $price,
                    'quantity' => $quantity,
                    'subtotal' => $subtotal,
                    'image' => $product['image_url'],
                    'stock_quantity' => $product['stock_quantity'],
                    'manage_stock' => $product['manage_stock']
                ];
            }
        }
        
        echo json_encode([
            'success' => true,
            'items' => $cartDetails,
            'total' => $total,
            'count' => count($cart),
            'formatted_total' => formatPrice($total, getSiteSettings()['currency'])
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}
?>
