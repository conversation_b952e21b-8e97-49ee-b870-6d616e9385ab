<?php
// Get featured products
$db->query("SELECT p.*, pi.image_url FROM products p 
           LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
           WHERE p.featured = 1 AND p.status = 'active' 
           ORDER BY p.created_at DESC LIMIT 8");
$featuredProducts = $db->resultset();

// Get categories
$db->query("SELECT * FROM categories WHERE status = 'active' AND parent_id IS NULL ORDER BY sort_order, name LIMIT 6");
$categories = $db->resultset();
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
    <div class="container mx-auto px-4 py-20">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    Welcome to <?= htmlspecialchars($siteSettings['site_name']) ?>
                </h1>
                <p class="text-xl mb-8 text-blue-100">
                    <?= htmlspecialchars($siteSettings['site_description']) ?>
                </p>
                <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                    <a href="?page=products" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center">
                        Shop Now
                    </a>
                    <a href="?page=about" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors text-center">
                        Learn More
                    </a>
                </div>
            </div>
            <div class="hidden lg:block">
                <img src="https://via.placeholder.com/600x400/4F46E5/FFFFFF?text=Hero+Image" alt="Hero Image" class="rounded-lg shadow-2xl">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shipping-fast text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Free Shipping</h3>
                <p class="text-gray-600">Free shipping on orders over $50</p>
            </div>
            <div class="text-center">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Secure Payment</h3>
                <p class="text-gray-600">100% secure payment processing</p>
            </div>
            <div class="text-center">
                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-undo text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Easy Returns</h3>
                <p class="text-gray-600">30-day return policy</p>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<?php if (!empty($categories)): ?>
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Shop by Category</h2>
            <p class="text-gray-600">Discover our wide range of products</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <?php foreach ($categories as $category): ?>
                <a href="?page=products&category=<?= $category['slug'] ?>" class="group">
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 text-center">
                        <?php if ($category['image']): ?>
                            <img src="<?= SITE_URL ?>/<?= $category['image'] ?>" alt="<?= htmlspecialchars($category['name']) ?>" 
                                 class="w-16 h-16 mx-auto mb-4 object-cover rounded-full">
                        <?php else: ?>
                            <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                                <i class="fas fa-tag text-gray-500 text-xl"></i>
                            </div>
                        <?php endif; ?>
                        <h3 class="font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
                            <?= htmlspecialchars($category['name']) ?>
                        </h3>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Products Section -->
<?php if (!empty($featuredProducts)): ?>
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Featured Products</h2>
            <p class="text-gray-600">Check out our most popular items</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
                    <div class="relative">
                        <a href="?page=product&id=<?= $product['id'] ?>">
                            <?php if ($product['image_url']): ?>
                                <img src="<?= SITE_URL ?>/<?= $product['image_url'] ?>" alt="<?= htmlspecialchars($product['name']) ?>" 
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-3xl"></i>
                                </div>
                            <?php endif; ?>
                        </a>
                        
                        <?php if ($product['sale_price']): ?>
                            <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs rounded">
                                Sale
                            </span>
                        <?php endif; ?>
                        
                        <?php if (getFeatureStatus('wishlist')): ?>
                            <button onclick="addToWishlist(<?= $product['id'] ?>)" 
                                    class="absolute top-2 right-2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                                <i class="fas fa-heart text-gray-400 hover:text-red-500"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-2 truncate">
                            <a href="?page=product&id=<?= $product['id'] ?>" class="hover:text-blue-600">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>
                        
                        <?php if ($product['short_description']): ?>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                <?= htmlspecialchars($product['short_description']) ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <?php if ($product['sale_price']): ?>
                                    <span class="text-lg font-bold text-red-600">
                                        <?= formatPrice($product['sale_price'], $siteSettings['currency']) ?>
                                    </span>
                                    <span class="text-sm text-gray-500 line-through">
                                        <?= formatPrice($product['price'], $siteSettings['currency']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-lg font-bold text-gray-800">
                                        <?= formatPrice($product['price'], $siteSettings['currency']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($product['stock_status'] === 'in_stock'): ?>
                                <button onclick="addToCart(<?= $product['id'] ?>)" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                    <i class="fas fa-cart-plus mr-1"></i> Add
                                </button>
                            <?php else: ?>
                                <span class="text-red-500 text-sm font-medium">Out of Stock</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-12">
            <a href="?page=products" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                View All Products
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Newsletter Section -->
<?php if (getFeatureStatus('newsletter')): ?>
<section class="py-16 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
        <p class="text-blue-100 mb-8">Subscribe to our newsletter for the latest products and offers</p>
        
        <form class="max-w-md mx-auto flex" onsubmit="subscribeNewsletter(event)">
            <input type="email" placeholder="Enter your email" required
                   class="flex-1 px-4 py-3 rounded-l-lg text-gray-800 focus:outline-none">
            <button type="submit" class="bg-blue-800 hover:bg-blue-900 px-6 py-3 rounded-r-lg font-semibold transition-colors">
                Subscribe
            </button>
        </form>
    </div>
</section>
<?php endif; ?>

<!-- Testimonials Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">What Our Customers Say</h2>
            <p class="text-gray-600">Don't just take our word for it</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"Excellent quality products and fast shipping. Highly recommended!"</p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                    <div>
                        <p class="font-semibold">Sarah Johnson</p>
                        <p class="text-sm text-gray-500">Verified Customer</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"Great customer service and amazing product selection. Will shop again!"</p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                    <div>
                        <p class="font-semibold">Mike Chen</p>
                        <p class="text-sm text-gray-500">Verified Customer</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"Love the website design and easy checkout process. Very user-friendly!"</p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                    <div>
                        <p class="font-semibold">Emily Davis</p>
                        <p class="text-sm text-gray-500">Verified Customer</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Add to wishlist function
    function addToWishlist(productId) {
        <?php if (isLoggedIn()): ?>
            fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            });
        <?php else: ?>
            showNotification('Please login to add items to wishlist', 'error');
            setTimeout(() => {
                window.location.href = '?page=login';
            }, 2000);
        <?php endif; ?>
    }
</script>
